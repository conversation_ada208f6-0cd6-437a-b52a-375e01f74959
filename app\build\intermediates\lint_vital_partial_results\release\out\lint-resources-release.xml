http://schemas.android.com/apk/res-auto;;${\:app*release*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/values-night/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/play_pause_button_state.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/settings_icon.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/record_button_state.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_selector.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_outline.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_outline_pressed.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/text_stroke_background.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_camera_preview.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/item_video_record.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*sourceProvider*0*resDir*0}/values/picker_styles.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V"#3F51B5";colorPrimaryDark,0,V"#303F9F";text_primary,0,V"#333333";background_light,0,V"#DEE2E5";black,0,V"#000000";text_secondary_adaptive,0,V"#666666";text_secondary_adaptive,1,V"#CCCCCC";grey,0,V"#F5F5F5";button_text_unselected,0,V"#808080";button_text_unselected,1,V"#FFFFFF";colorAccent,0,V"#FF4081";background_adaptive,0,V"#DEE2E5";background_adaptive,1,V"#000000";white,0,V"#FFFFFF";text_adaptive,0,V"#000000";text_adaptive,1,V"#FFFFFF";container_adaptive,0,V"#F5F5F5";container_adaptive,1,V"#1A1A1A";+dimen:card_corner_radius,2,V"8dp";+drawable:button_background,3,F;play_pause_button_state,4,F;settings_icon,5,F;record_button_state,6,F;button_selector,7,F;ic_launcher_foreground,8,F;ic_launcher_background,9,F;button_outline,10,F;button_outline_pressed,11,F;text_stroke_background,12,F;+id:video_playback_page,13,F;video_player_container,13,F;left_camera_container,13,F;live_monitor_page,13,F;back_camera_container,13,F;left_camera_view,13,F;btn_playback_speed,13,F;btn_filter_back,13,F;camera_surface_view,14,F;tv_datetime_display,13,F;back_camera_view,13,F;tv_camera_direction,15,F;video_progress_bar,13,F;video_player_card,13,F;recycler_video_list,13,F;main_video_player,13,F;content_container,13,F;btn_sentinel_monitor,13,F;right_camera_container,13,F;front_camera_container,13,F;btn_video_playback,13,F;btn_filter_left,13,F;tv_current_time,13,F;picker_day,13,F;player_controls_overlay,13,F;right_content_container,13,F;tv_record_time,15,F;btn_all_cameras,13,F;btn_filter_front,13,F;btn_filter_right,13,F;video_player_page,13,F;btnStartStop,14,F;bottom_control_bar,13,F;btn_filter_all,13,F;front_camera_view,13,F;sentinel_monitor_page,13,F;right_preview_area,13,F;page_selector_container,13,F;picker_month,13,F;btn_play_pause,13,F;left_control_panel,13,F;switch_sentry_auto,13,F;picker_hour,13,F;btn_settings,13,F;right_camera_view,13,F;+layout:activity_main,13,F;activity_camera_preview,14,F;item_video_record,15,F;+mipmap:ic_launcher_round,16,F;ic_launcher_round,17,F;ic_launcher_round,18,F;ic_launcher_round,19,F;ic_launcher_round,20,F;ic_launcher_round,21,F;ic_launcher,22,F;ic_launcher,23,F;ic_launcher,24,F;ic_launcher,25,F;ic_launcher,26,F;ic_launcher,27,F;+string:camera_format,28,V"相机格式\:";test_preparation,28,V"选择测试项目开始测试";stop_test,28,V"停止测试";test4_title,28,V"相机格式测试";codec_selection,28,V"编码器\:";app_name,28,V"哨兵监控";test2_title,28,V"文件转码测试";start_test,28,V"开始测试";test3_title,28,V"视频播放同时录制";resolution_selection,28,V"分辨率\:";bitrate_selection,28,V"比特率\:";test1_title,28,V"摄像头直接编码测试";camera_selection,28,V"摄像头\:";+style:Base.Theme.Sbjk,29,VDTheme.Material3.DayNight.NoActionBar,;Base.Theme.Sbjk,30,VDTheme.Material3.DayNight.NoActionBar,;CustomTextView,29,VNandroid\:textSize:14sp,android\:textColor:@color/text_primary,;NumberPickerStyle,31,VNandroid\:textSize:14sp,android\:textColor:@color/text_adaptive,android\:background:@android\:color/transparent,android\:gravity:center,;CustomButton,29,VDWidget.AppCompat.Button.Borderless,android\:textSize:14sp,android\:padding:0dp,android\:textColor:@color/colorPrimary,android\:drawableTint:@color/white,android\:minWidth:0dp,android\:minHeight:0dp,android\:includeFontPadding:false,;Theme.Sbjk,29,VDTheme.AppCompat.Light.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@color/colorPrimaryDark,android\:statusBarColor:@color/background_light,android\:windowLightStatusBar:true,android\:windowBackground:@color/background_light,;Theme.Sbjk,30,VDTheme.AppCompat.DayNight.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@android\:color/black,android\:statusBarColor:@android\:color/black,android\:windowBackground:@android\:color/black,;+xml:data_extraction_rules,32,F;backup_rules,33,F;