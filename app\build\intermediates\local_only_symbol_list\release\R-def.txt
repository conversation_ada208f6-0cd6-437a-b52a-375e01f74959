R_DEF: Internal format may change without notice
local
color background_adaptive
color background_light
color black
color button_text_unselected
color colorAccent
color colorPrimary
color colorPrimaryDark
color container_adaptive
color grey
color text_adaptive
color text_primary
color text_secondary_adaptive
color white
dimen card_corner_radius
drawable button_background
drawable button_outline
drawable button_outline_pressed
drawable button_selector
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable play_pause_button_state
drawable record_button_state
drawable settings_icon
drawable text_stroke_background
id back_camera_container
id back_camera_view
id bottom_control_bar
id btnStartStop
id btn_all_cameras
id btn_filter_all
id btn_filter_back
id btn_filter_front
id btn_filter_left
id btn_filter_right
id btn_play_pause
id btn_playback_speed
id btn_sentinel_monitor
id btn_settings
id btn_video_playback
id camera_surface_view
id content_container
id front_camera_container
id front_camera_view
id left_camera_container
id left_camera_view
id left_control_panel
id live_monitor_page
id main_video_player
id page_selector_container
id picker_day
id picker_hour
id picker_month
id player_controls_overlay
id recycler_video_list
id right_camera_container
id right_camera_view
id right_content_container
id right_preview_area
id sentinel_monitor_page
id switch_sentry_auto
id tv_camera_direction
id tv_current_time
id tv_datetime_display
id tv_record_time
id video_playback_page
id video_player_card
id video_player_container
id video_player_page
id video_progress_bar
layout activity_camera_preview
layout activity_main
layout item_video_record
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string bitrate_selection
string camera_format
string camera_selection
string codec_selection
string resolution_selection
string start_test
string stop_test
string test1_title
string test2_title
string test3_title
string test4_title
string test_preparation
style Base.Theme.Sbjk
style CustomButton
style CustomTextView
style NumberPickerStyle
style Theme.Sbjk
xml backup_rules
xml data_extraction_rules
