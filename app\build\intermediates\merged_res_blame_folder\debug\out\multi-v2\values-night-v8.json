{"logs": [{"outputFile": "com.autolink.sbjk.app-mergeDebugResources-35:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "437,507,591,675,771,873,975,4679", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "502,586,670,766,868,970,1064,4763"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "7,44", "startColumns": "4,4", "startOffsets": "325,3957", "endLines": "10,57", "endColumns": "12,12", "endOffsets": "432,4674"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "4,7,6,3,5", "startColumns": "4,4,4,4,4", "startOffsets": "142,364,291,77,215", "endColumns": "53,56,52,47,57", "endOffsets": "191,416,339,120,268"}, "to": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,109,166,219,267", "endColumns": "53,56,52,47,57", "endOffsets": "104,161,214,262,320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1069,1144,1255,1344,1445,1552,1659,1758,1865,1968,2095,2183,2307,2409,2511,2627,2729,2843,2971,3087,3209,3345,3465,3599,3719,3831,4768,4885,5009,5139,5261,5399,5533,5649", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1139,1250,1339,1440,1547,1654,1753,1860,1963,2090,2178,2302,2404,2506,2622,2724,2838,2966,3082,3204,3340,3460,3594,3714,3826,3952,4880,5004,5134,5256,5394,5528,5644,5764"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-mergeDebugResources-35:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "6,7,8,9,10,11,12,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "167,237,321,405,501,603,705,4409", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "232,316,400,496,598,700,794,4493"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,8", "startColumns": "4,4", "startOffsets": "100,349", "endLines": "5,21", "endColumns": "12,12", "endOffsets": "311,1108"}, "to": {"startLines": "2,39", "startColumns": "4,4", "startOffsets": "55,3687", "endLines": "5,52", "endColumns": "12,12", "endOffsets": "162,4404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "799,874,985,1074,1175,1282,1389,1488,1595,1698,1825,1913,2037,2139,2241,2357,2459,2573,2701,2817,2939,3075,3195,3329,3449,3561,4498,4615,4739,4869,4991,5129,5263,5379", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "869,980,1069,1170,1277,1384,1483,1590,1693,1820,1908,2032,2134,2236,2352,2454,2568,2696,2812,2934,3070,3190,3324,3444,3556,3682,4610,4734,4864,4986,5124,5258,5374,5494"}}]}]}