<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".MainActivity">

    <!-- 左侧控制区域 (25%) -->
    <LinearLayout
        android:id="@+id/left_control_panel"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@android:color/black"
        app:layout_constraintWidth_percent="0.25"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_preview_area">

        <!-- 顶部页面选择栏 -->
        <LinearLayout
            android:id="@+id/page_selector_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center">

            <!-- 哨兵监控页面选择 -->
            <TextView
                android:id="@+id/btn_sentinel_monitor"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="哨兵监控"
                android:textSize="16sp"
                android:textColor="#808080"
                android:gravity="center"
                android:padding="12dp"
                android:clickable="true"
                android:focusable="true"/>

            <!-- 录像回放页面选择 -->
            <TextView
                android:id="@+id/btn_video_playback"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="录像回放"
                android:textSize="16sp"
                android:textColor="#808080"
                android:gravity="center"
                android:padding="12dp"
                android:clickable="true"
                android:focusable="true"/>

        </LinearLayout>

        <!-- 中间内容区域 -->
        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="16dp">

            <!-- 哨兵监控页面内容 -->
            <LinearLayout
                android:id="@+id/sentinel_monitor_page"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="visible">

                <!-- 哨兵自动模式开关 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="自动启动哨兵功能"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:layout_marginEnd="12dp"/>

                    <Switch
                        android:id="@+id/switch_sentry_auto"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"/>

                </LinearLayout>

                <!-- 录制控制按钮 -->
                <Button
                    android:id="@+id/btn_all_cameras"
                    android:layout_width="60dp"
                    android:layout_height="55dp"
                    android:drawableStart="@drawable/play_pause_button_state"
                    android:paddingStart="13dp"
                    android:paddingEnd="10dp"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:text=""
                    android:background="@drawable/button_background"
                    style="@style/CustomButton"/>

            </LinearLayout>

            <!-- 录像回放页面内容 -->
            <LinearLayout
                android:id="@+id/video_playback_page"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- 筛选控制区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="8dp">

                    <!-- 摄像头筛选按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_marginBottom="8dp">

                        <Button
                            android:id="@+id/btn_filter_all"
                            android:layout_width="0dp"
                            android:layout_height="32dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="2dp"
                            android:text="全部"
                            android:textSize="13sp"
                            android:textColor="@color/text_adaptive"
                            android:background="@drawable/button_background"/>

                        <Button
                            android:id="@+id/btn_filter_front"
                            android:layout_width="0dp"
                            android:layout_height="32dp"
                            android:layout_weight="1"
                            android:layout_marginStart="2dp"
                            android:layout_marginEnd="2dp"
                            android:text="前视"
                            android:textSize="13sp"
                            android:textColor="@color/button_text_unselected"
                            android:background="@drawable/button_outline"/>

                        <Button
                            android:id="@+id/btn_filter_back"
                            android:layout_width="0dp"
                            android:layout_height="32dp"
                            android:layout_weight="1"
                            android:layout_marginStart="2dp"
                            android:layout_marginEnd="2dp"
                            android:text="后视"
                            android:textSize="13sp"
                            android:textColor="@color/button_text_unselected"
                            android:background="@drawable/button_outline"/>

                        <Button
                            android:id="@+id/btn_filter_left"
                            android:layout_width="0dp"
                            android:layout_height="32dp"
                            android:layout_weight="1"
                            android:layout_marginStart="2dp"
                            android:layout_marginEnd="2dp"
                            android:text="左视"
                            android:textSize="13sp"
                            android:textColor="@color/button_text_unselected"
                            android:background="@drawable/button_outline"/>

                        <Button
                            android:id="@+id/btn_filter_right"
                            android:layout_width="0dp"
                            android:layout_height="32dp"
                            android:layout_weight="1"
                            android:layout_marginStart="2dp"
                            android:text="右视"
                            android:textSize="13sp"
                            android:textColor="@color/button_text_unselected"
                            android:background="@drawable/button_outline"/>

                    </LinearLayout>

                    <!-- 苹果风格时间选择器 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_marginBottom="8dp">

                        <!-- 月份选择器 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="月"
                                android:textColor="@color/text_adaptive"
                                android:textSize="12sp"
                                android:layout_marginBottom="4dp"/>

                            <NumberPicker
                                android:id="@+id/picker_month"
                                android:layout_width="60dp"
                                android:layout_height="80dp"
                                android:theme="@style/NumberPickerStyle"/>

                        </LinearLayout>

                        <!-- 日期选择器 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="日"
                                android:textColor="@color/text_adaptive"
                                android:textSize="12sp"
                                android:layout_marginBottom="4dp"/>

                            <NumberPicker
                                android:id="@+id/picker_day"
                                android:layout_width="60dp"
                                android:layout_height="80dp"
                                android:theme="@style/NumberPickerStyle"/>

                        </LinearLayout>

                        <!-- 小时选择器 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="时"
                                android:textColor="@color/text_adaptive"
                                android:textSize="12sp"
                                android:layout_marginBottom="4dp"/>

                            <NumberPicker
                                android:id="@+id/picker_hour"
                                android:layout_width="60dp"
                                android:layout_height="80dp"
                                android:theme="@style/NumberPickerStyle"/>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- 视频列表区域 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_video_list"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:padding="8dp"/>

            </LinearLayout>

        </FrameLayout>

        <!-- 底部控制栏 -->
        <LinearLayout
            android:id="@+id/bottom_control_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="end">

            <!-- 设置按钮 -->
            <Button
                android:id="@+id/btn_settings"
                android:layout_width="60dp"
                android:layout_height="55dp"
                android:drawableStart="@drawable/settings_icon"
                android:paddingStart="15dp"
                android:paddingEnd="12dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:background="@drawable/button_background"
                style="@style/CustomButton"/>

        </LinearLayout>

    </LinearLayout>

    <!-- 右侧预览区域 (75%) -->
    <LinearLayout
        android:id="@+id/right_preview_area"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="2dp"
        app:layout_constraintWidth_percent="0.75"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/left_control_panel"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 时间显示栏 -->
        <TextView
            android:id="@+id/tv_datetime_display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2024年12月19日 14:30:25"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:padding="12dp"/>

        <!-- 右侧内容容器 -->
        <FrameLayout
            android:id="@+id/right_content_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- 实时监控页面 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/live_monitor_page"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible">

        <!-- 前置摄像头视图 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/front_camera_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/left_camera_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/back_camera_container">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <SurfaceView
                    android:id="@+id/front_camera_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|start"
                    android:layout_margin="8dp"
                    android:text="前视"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1.5"
                    android:shadowDy="1.5"
                    android:shadowRadius="3"
                    android:background="@drawable/text_stroke_background"
                    android:padding="6dp"/>
            </FrameLayout>
        </androidx.cardview.widget.CardView>

        <!-- 后置摄像头视图 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/back_camera_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/right_camera_container"
            app:layout_constraintStart_toEndOf="@id/front_camera_container"
            app:layout_constraintEnd_toEndOf="parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <SurfaceView
                    android:id="@+id/back_camera_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|start"
                    android:layout_margin="8dp"
                    android:text="后视"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1.5"
                    android:shadowDy="1.5"
                    android:shadowRadius="3"
                    android:background="@drawable/text_stroke_background"
                    android:padding="6dp"/>
            </FrameLayout>
        </androidx.cardview.widget.CardView>

        <!-- 左侧摄像头视图 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/left_camera_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/front_camera_container"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/right_camera_container">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <SurfaceView
                    android:id="@+id/left_camera_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|start"
                    android:layout_margin="8dp"
                    android:text="左视"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1.5"
                    android:shadowDy="1.5"
                    android:shadowRadius="3"
                    android:background="@drawable/text_stroke_background"
                    android:padding="6dp"/>
            </FrameLayout>
        </androidx.cardview.widget.CardView>

        <!-- 右侧摄像头视图 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/right_camera_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="4dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/back_camera_container"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/left_camera_container"
            app:layout_constraintEnd_toEndOf="parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <SurfaceView
                    android:id="@+id/right_camera_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|start"
                    android:layout_margin="8dp"
                    android:text="右视"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1.5"
                    android:shadowDy="1.5"
                    android:shadowRadius="3"
                    android:background="@drawable/text_stroke_background"
                    android:padding="6dp"/>
            </FrameLayout>
        </androidx.cardview.widget.CardView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 回放播放器页面 -->
            <LinearLayout
                android:id="@+id/video_player_page"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- 播放器区域 -->
                <FrameLayout
                    android:id="@+id/video_player_container"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@android:color/black">

                    <!-- 主播放器 -->
                    <com.autolink.sbjk.ui.widget.FullScreenVideoView
                        android:id="@+id/main_video_player"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"/>

                </FrameLayout>

                <!-- 独立的播放器控制栏 -->
                <LinearLayout
                    android:id="@+id/player_controls_overlay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#CC000000"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <!-- 播放/暂停按钮 -->
                    <Button
                        android:id="@+id/btn_play_pause"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:text="▶"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:background="@drawable/button_background"
                        android:layout_marginEnd="12dp"/>

                    <!-- 进度条 -->
                    <SeekBar
                        android:id="@+id/video_progress_bar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="12dp"
                        android:progressTint="@android:color/white"
                        android:thumbTint="@android:color/white"/>

                    <!-- 倍速按钮 -->
                    <Button
                        android:id="@+id/btn_playback_speed"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:text="1.0x"
                        android:textColor="@android:color/white"
                        android:textSize="11sp"
                        android:background="@drawable/button_outline"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:minWidth="48dp"
                        android:layout_marginEnd="8dp"/>

                    <!-- 时间显示 -->
                    <TextView
                        android:id="@+id/tv_current_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00:00"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:minWidth="40dp"
                        android:gravity="center"/>

                </LinearLayout>

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>