package com.autolink.sbjk.adapter;

import android.content.res.Configuration;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.autolink.sbjk.R;
import com.autolink.sbjk.model.VideoRecordInfo;

/**
 * 录像列表适配器
 * 使用ListAdapter实现高效的列表更新
 */
public class VideoListAdapter extends ListAdapter<VideoRecordInfo, VideoListAdapter.VideoViewHolder> {
    
    // 点击事件监听器
    public interface OnVideoClickListener {
        void onVideoClick(VideoRecordInfo video);
    }
    
    private OnVideoClickListener clickListener;
    
    public VideoListAdapter() {
        super(new VideoDiffCallback());
    }
    
    /**
     * 设置点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.clickListener = listener;
    }
    
    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_video_record, parent, false);
        return new VideoViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        VideoRecordInfo video = getItem(position);
        holder.bind(video, clickListener);
    }
    
    /**
     * ViewHolder类
     */
    static class VideoViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView cameraText;
        private final TextView timeText;
        private final View itemView;
        
        public VideoViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            this.cameraText = itemView.findViewById(R.id.tv_camera_direction);
            this.timeText = itemView.findViewById(R.id.tv_record_time);
        }
        
        public void bind(VideoRecordInfo video, OnVideoClickListener clickListener) {
            if (video != null) {
                // 设置摄像头方向
                cameraText.setText(video.getCameraDirection());

                // 设置录制时间
                timeText.setText(video.getDisplayTime());

                // 根据日夜模式设置颜色
                updateColors();

                // 设置点击事件
                itemView.setOnClickListener(v -> {
                    if (clickListener != null) {
                        clickListener.onVideoClick(video);
                    }
                });

                // 设置选中状态的视觉效果（可选）
                itemView.setSelected(false);
            }
        }

        /**
         * 根据日夜模式更新颜色
         */
        private void updateColors() {
            boolean isDarkMode = (itemView.getContext().getResources().getConfiguration().uiMode &
                Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES;

            if (isDarkMode) {
                // 深色模式
                cameraText.setTextColor(Color.WHITE);
                timeText.setTextColor(Color.parseColor("#CCCCCC"));
                itemView.setBackgroundColor(Color.BLACK);
            } else {
                // 浅色模式
                cameraText.setTextColor(Color.BLACK);
                timeText.setTextColor(Color.parseColor("#666666"));
                itemView.setBackgroundColor(Color.parseColor("#DEE2E5"));
            }
        }
    }
    
    /**
     * DiffUtil回调，用于高效更新列表
     */
    private static class VideoDiffCallback extends DiffUtil.ItemCallback<VideoRecordInfo> {
        
        @Override
        public boolean areItemsTheSame(@NonNull VideoRecordInfo oldItem, @NonNull VideoRecordInfo newItem) {
            // 使用文件路径作为唯一标识
            return oldItem.getFilePath().equals(newItem.getFilePath());
        }
        
        @Override
        public boolean areContentsTheSame(@NonNull VideoRecordInfo oldItem, @NonNull VideoRecordInfo newItem) {
            // 比较所有显示相关的内容
            return oldItem.getCameraDirection().equals(newItem.getCameraDirection()) &&
                   oldItem.getDisplayTime().equals(newItem.getDisplayTime()) &&
                   oldItem.getTimestamp() == newItem.getTimestamp() &&
                   oldItem.getFileSize() == newItem.getFileSize();
        }
    }
}
