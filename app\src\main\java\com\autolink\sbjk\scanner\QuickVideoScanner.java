package com.autolink.sbjk.scanner;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.model.VideoRecordInfo;
import com.autolink.sbjk.common.util.LogUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 高性能视频文件扫描器
 * 只扫描.mp4文件，排除.tmp录制中文件
 * 采用并行处理提升扫描性能
 */
public class QuickVideoScanner {
    
    private static final String TAG = "QuickVideoScanner";
    
    // 录像存储路径
    private static final String RECORD_PATH = CameraConstants.DEFAULT_RECORD_PATH;
    private static final String BACKUP_RECORD_PATH = CameraConstants.BACKUP_RECORD_PATH;

    // 摄像头子目录
    private static final String[] CAMERA_SUBDIRS = {"前视", "后视", "左视", "右视"};
    
    // 线程池，用于并行处理
    private volatile ExecutorService executorService;

    public QuickVideoScanner() {
        initializeExecutorService();
    }

    /**
     * 初始化线程池
     */
    private synchronized void initializeExecutorService() {
        if (executorService == null || executorService.isShutdown()) {
            // 创建固定大小的线程池，最多使用CPU核心数的线程
            int threadCount = Math.min(4, Runtime.getRuntime().availableProcessors());
            executorService = Executors.newFixedThreadPool(threadCount);
            LogUtil.d(TAG, "QuickVideoScanner线程池已初始化");
        }
    }
    
    /**
     * 快速扫描所有录像文件
     * @return 录像文件列表，按时间倒序排列
     */
    public CompletableFuture<List<VideoRecordInfo>> quickScanAsync() {
        // 确保线程池可用
        initializeExecutorService();

        return CompletableFuture.supplyAsync(() -> {
            List<VideoRecordInfo> allVideos = new ArrayList<>();
            
            // 扫描主存储路径
            List<VideoRecordInfo> mainVideos = scanDirectory(RECORD_PATH);
            allVideos.addAll(mainVideos);
            
            // 扫描备用存储路径（如果不同）
            if (!RECORD_PATH.equals(BACKUP_RECORD_PATH)) {
                List<VideoRecordInfo> backupVideos = scanDirectory(BACKUP_RECORD_PATH);
                allVideos.addAll(backupVideos);
            }
            
            // 按时间戳倒序排列（最新的在前面）
            allVideos.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
            
            LogUtil.i(TAG, "扫描完成，找到 " + allVideos.size() + " 个录像文件");
            return allVideos;
            
        }, executorService);
    }
    
    /**
     * 同步扫描方法（用于需要立即结果的场景）
     */
    public List<VideoRecordInfo> quickScan() {
        try {
            return quickScanAsync().get();
        } catch (Exception e) {
            LogUtil.e(TAG, "同步扫描失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 扫描指定目录（包括摄像头子目录）
     */
    private List<VideoRecordInfo> scanDirectory(String directoryPath) {
        List<VideoRecordInfo> videos = new ArrayList<>();

        try {
            File recordDir = new File(directoryPath);

            if (!recordDir.exists()) {
                LogUtil.d(TAG, "录像目录不存在: " + directoryPath);
                return videos;
            }

            if (!recordDir.isDirectory()) {
                LogUtil.w(TAG, "路径不是目录: " + directoryPath);
                return videos;
            }

            // 扫描各个摄像头的子目录
            for (String cameraSubdir : CAMERA_SUBDIRS) {
                File cameraDir = new File(recordDir, cameraSubdir);
                if (cameraDir.exists() && cameraDir.isDirectory()) {
                    List<VideoRecordInfo> cameraVideos = scanCameraDirectory(cameraDir);
                    videos.addAll(cameraVideos);
                    LogUtil.d(TAG, "在 " + cameraSubdir + " 目录中找到 " + cameraVideos.size() + " 个录像文件");
                } else {
                    LogUtil.d(TAG, "摄像头目录不存在或不是目录: " + cameraDir.getAbsolutePath());
                }
            }

            LogUtil.d(TAG, "总共扫描到 " + videos.size() + " 个录像文件");

        } catch (Exception e) {
            LogUtil.e(TAG, "扫描目录失败: " + directoryPath, e);
        }

        return videos;
    }

    /**
     * 扫描单个摄像头目录
     */
    private List<VideoRecordInfo> scanCameraDirectory(File cameraDir) {
        List<VideoRecordInfo> videos = new ArrayList<>();

        try {
            // 只扫描.mp4文件，排除.tmp文件
            File[] files = cameraDir.listFiles((dir, name) -> {
                String lowerName = name.toLowerCase();
                return lowerName.endsWith(".mp4") || lowerName.endsWith(".mkv");
            });

            if (files != null && files.length > 0) {
                // 并行解析文件信息
                videos = parseFilesParallel(files);
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "扫描摄像头目录失败: " + cameraDir.getAbsolutePath(), e);
        }

        return videos;
    }
    
    /**
     * 并行解析文件信息
     */
    private List<VideoRecordInfo> parseFilesParallel(File[] files) {
        List<VideoRecordInfo> validVideos = new ArrayList<>();
        
        try {
            // 使用并行流处理文件解析
            Arrays.stream(files)
                .parallel()
                .map(file -> new VideoRecordInfo(file.getAbsolutePath()))
                .filter(VideoRecordInfo::isValid) // 只保留有效的录像信息
                .forEachOrdered(validVideos::add);
                
        } catch (Exception e) {
            LogUtil.e(TAG, "并行解析文件失败", e);
            
            // 如果并行处理失败，回退到串行处理
            for (File file : files) {
                try {
                    VideoRecordInfo info = new VideoRecordInfo(file.getAbsolutePath());
                    if (info.isValid()) {
                        validVideos.add(info);
                    }
                } catch (Exception ex) {
                    LogUtil.w(TAG, "解析文件失败: " + file.getName(), ex);
                }
            }
        }
        
        return validVideos;
    }
    
    /**
     * 检查新完成的录像文件
     * @param lastScanTime 上次扫描时间
     * @return 新完成的录像文件列表
     */
    public List<VideoRecordInfo> checkNewCompletedFiles(long lastScanTime) {
        List<VideoRecordInfo> newFiles = new ArrayList<>();
        
        try {
            // 检查主存储路径
            newFiles.addAll(checkNewFilesInDirectory(RECORD_PATH, lastScanTime));
            
            // 检查备用存储路径
            if (!RECORD_PATH.equals(BACKUP_RECORD_PATH)) {
                newFiles.addAll(checkNewFilesInDirectory(BACKUP_RECORD_PATH, lastScanTime));
            }
            
            // 按时间排序
            newFiles.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
            
        } catch (Exception e) {
            LogUtil.e(TAG, "检查新文件失败", e);
        }
        
        return newFiles;
    }
    
    /**
     * 检查指定目录中的新文件（包括摄像头子目录）
     */
    private List<VideoRecordInfo> checkNewFilesInDirectory(String directoryPath, long lastScanTime) {
        List<VideoRecordInfo> newFiles = new ArrayList<>();

        try {
            File recordDir = new File(directoryPath);

            if (!recordDir.exists()) {
                return newFiles;
            }

            // 检查各个摄像头子目录中的新文件
            for (String cameraSubdir : CAMERA_SUBDIRS) {
                File cameraDir = new File(recordDir, cameraSubdir);
                if (cameraDir.exists() && cameraDir.isDirectory()) {
                    List<VideoRecordInfo> cameraNewFiles = checkNewFilesInCameraDirectory(cameraDir, lastScanTime);
                    newFiles.addAll(cameraNewFiles);
                }
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "检查目录新文件失败: " + directoryPath, e);
        }

        return newFiles;
    }

    /**
     * 检查单个摄像头目录中的新文件
     */
    private List<VideoRecordInfo> checkNewFilesInCameraDirectory(File cameraDir, long lastScanTime) {
        List<VideoRecordInfo> newFiles = new ArrayList<>();

        try {
            File[] files = cameraDir.listFiles((dir, name) -> {
                File file = new File(dir, name);
                String lowerName = name.toLowerCase();
                return (lowerName.endsWith(".mp4") || lowerName.endsWith(".mkv"))
                    && file.lastModified() > lastScanTime;
            });

            if (files != null) {
                for (File file : files) {
                    try {
                        VideoRecordInfo info = new VideoRecordInfo(file.getAbsolutePath());
                        if (info.isValid()) {
                            newFiles.add(info);
                        }
                    } catch (Exception e) {
                        LogUtil.w(TAG, "解析新文件失败: " + file.getName(), e);
                    }
                }
            }

        } catch (Exception e) {
            LogUtil.e(TAG, "检查摄像头目录新文件失败: " + cameraDir.getAbsolutePath(), e);
        }

        return newFiles;
    }
    
    /**
     * 按摄像头方向筛选录像
     */
    public List<VideoRecordInfo> filterByCamera(List<VideoRecordInfo> videos, String cameraDirection) {
        if (cameraDirection == null || cameraDirection.equals("全部")) {
            return new ArrayList<>(videos);
        }
        
        List<VideoRecordInfo> filtered = new ArrayList<>();
        for (VideoRecordInfo video : videos) {
            if (cameraDirection.equals(video.getCameraDirection())) {
                filtered.add(video);
            }
        }
        
        return filtered;
    }
    
    /**
     * 按时间范围筛选录像
     */
    public List<VideoRecordInfo> filterByTimeRange(List<VideoRecordInfo> videos, long startTime, long endTime) {
        List<VideoRecordInfo> filtered = new ArrayList<>();
        
        for (VideoRecordInfo video : videos) {
            long timestamp = video.getTimestamp();
            if (timestamp >= startTime && timestamp <= endTime) {
                filtered.add(video);
            }
        }
        
        return filtered;
    }
    
    /**
     * 释放资源
     */
    public void shutdown() {
        try {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();

                // 等待正在执行的任务完成
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                    LogUtil.w(TAG, "强制关闭扫描器线程池");
                }
            }

            LogUtil.d(TAG, "QuickVideoScanner已关闭");

        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
            LogUtil.w(TAG, "关闭扫描器时被中断", e);
        } catch (Exception e) {
            LogUtil.e(TAG, "关闭扫描器时出错", e);
        }
    }
}
