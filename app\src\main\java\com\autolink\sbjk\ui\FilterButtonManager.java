package com.autolink.sbjk.ui;

import android.content.res.Configuration;
import android.graphics.Color;
import android.widget.Button;

import com.autolink.sbjk.R;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 筛选按钮管理器
 * 管理摄像头和时间筛选按钮的状态和点击事件
 */
public class FilterButtonManager {
    
    private static final String TAG = "FilterButtonManager";
    
    // 筛选回调接口
    public interface OnFilterChangeListener {
        void onCameraFilterChanged(String cameraFilter);
        void onTimeFilterChanged(String timeFilter);
    }
    
    private OnFilterChangeListener filterListener;
    
    // 摄像头筛选按钮
    private final List<Button> cameraButtons = new ArrayList<>();
    private final String[] cameraFilters = {"全部", "前视", "后视", "左视", "右视"};
    private String currentCameraFilter = "全部";
    
    // 时间筛选按钮
    private final List<Button> timeButtons = new ArrayList<>();
    private final String[] timeFilters = {"全部", "月", "日", "时"};
    private String currentTimeFilter = "全部";
    
    /**
     * 设置筛选监听器
     */
    public void setOnFilterChangeListener(OnFilterChangeListener listener) {
        this.filterListener = listener;
    }
    
    /**
     * 初始化摄像头筛选按钮
     */
    public void initCameraButtons(Button btnAll, Button btnFront, Button btnBack, Button btnLeft, Button btnRight) {
        cameraButtons.clear();
        cameraButtons.add(btnAll);
        cameraButtons.add(btnFront);
        cameraButtons.add(btnBack);
        cameraButtons.add(btnLeft);
        cameraButtons.add(btnRight);
        
        // 设置点击事件
        for (int i = 0; i < cameraButtons.size(); i++) {
            final int index = i;
            final String filter = cameraFilters[i];
            cameraButtons.get(i).setOnClickListener(v -> selectCameraFilter(index, filter));
        }
        
        // 设置默认选中状态
        selectCameraFilter(0, "全部");
        
        LogUtil.d(TAG, "摄像头筛选按钮初始化完成");
    }
    
    /**
     * 初始化时间筛选按钮
     */
    public void initTimeButtons(Button btnAll, Button btnMonth, Button btnDay, Button btnHour) {
        timeButtons.clear();
        timeButtons.add(btnAll);
        timeButtons.add(btnMonth);
        timeButtons.add(btnDay);
        timeButtons.add(btnHour);
        
        // 设置点击事件
        for (int i = 0; i < timeButtons.size(); i++) {
            final int index = i;
            final String filter = timeFilters[i];
            timeButtons.get(i).setOnClickListener(v -> selectTimeFilter(index, filter));
        }
        
        // 设置默认选中状态
        selectTimeFilter(0, "全部");
        
        LogUtil.d(TAG, "时间筛选按钮初始化完成");
    }
    
    /**
     * 选择摄像头筛选
     */
    private void selectCameraFilter(int selectedIndex, String filter) {
        if (filter.equals(currentCameraFilter)) {
            return; // 已经是当前选中的筛选，不需要重复处理
        }
        
        currentCameraFilter = filter;
        
        // 更新按钮状态
        updateCameraButtonStates(selectedIndex);
        
        // 通知监听器
        if (filterListener != null) {
            filterListener.onCameraFilterChanged(filter);
        }
        
        LogUtil.d(TAG, "选择摄像头筛选: " + filter);
    }
    
    /**
     * 选择时间筛选
     */
    private void selectTimeFilter(int selectedIndex, String filter) {
        if (filter.equals(currentTimeFilter)) {
            return; // 已经是当前选中的筛选，不需要重复处理
        }
        
        currentTimeFilter = filter;
        
        // 更新按钮状态
        updateTimeButtonStates(selectedIndex);
        
        // 通知监听器
        if (filterListener != null) {
            filterListener.onTimeFilterChanged(filter);
        }
        
        LogUtil.d(TAG, "选择时间筛选: " + filter);
    }
    
    /**
     * 更新摄像头按钮状态
     */
    private void updateCameraButtonStates(int selectedIndex) {
        for (int i = 0; i < cameraButtons.size(); i++) {
            Button button = cameraButtons.get(i);
            boolean isDarkMode = (button.getContext().getResources().getConfiguration().uiMode &
                Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES;

            if (i == selectedIndex) {
                // 选中状态
                button.setBackgroundResource(R.drawable.button_background);
                if (isDarkMode) {
                    button.setTextColor(Color.BLACK); // 深色模式下选中按钮文字为黑色
                } else {
                    button.setTextColor(Color.BLACK); // 浅色模式下选中按钮文字为黑色
                }
            } else {
                // 未选中状态
                button.setBackgroundResource(R.drawable.button_outline);
                if (isDarkMode) {
                    button.setTextColor(Color.WHITE); // 深色模式下未选中按钮文字为白色
                } else {
                    button.setTextColor(0xFF808080); // 浅色模式下未选中按钮文字为灰色
                }
            }
        }
    }
    
    /**
     * 更新时间按钮状态
     */
    private void updateTimeButtonStates(int selectedIndex) {
        for (int i = 0; i < timeButtons.size(); i++) {
            Button button = timeButtons.get(i);
            if (i == selectedIndex) {
                // 选中状态
                button.setBackgroundResource(R.drawable.button_background);
                button.setTextColor(button.getContext().getResources().getColor(android.R.color.black));
            } else {
                // 未选中状态
                button.setBackgroundResource(R.drawable.button_outline);
                button.setTextColor(0xFF808080); // 灰色
            }
        }
    }
    
    /**
     * 程序化设置摄像头筛选（不触发回调）
     */
    public void setCameraFilter(String filter) {
        for (int i = 0; i < cameraFilters.length; i++) {
            if (cameraFilters[i].equals(filter)) {
                currentCameraFilter = filter;
                updateCameraButtonStates(i);
                break;
            }
        }
    }
    
    /**
     * 程序化设置时间筛选（不触发回调）
     */
    public void setTimeFilter(String filter) {
        for (int i = 0; i < timeFilters.length; i++) {
            if (timeFilters[i].equals(filter)) {
                currentTimeFilter = filter;
                updateTimeButtonStates(i);
                break;
            }
        }
    }
    
    /**
     * 重置所有筛选
     */
    public void resetFilters() {
        selectCameraFilter(0, "全部");
        selectTimeFilter(0, "全部");
        LogUtil.d(TAG, "重置所有筛选");
    }
    
    /**
     * 获取当前摄像头筛选
     */
    public String getCurrentCameraFilter() {
        return currentCameraFilter;
    }
    
    /**
     * 获取当前时间筛选
     */
    public String getCurrentTimeFilter() {
        return currentTimeFilter;
    }
    
    /**
     * 获取筛选描述
     */
    public String getFilterDescription() {
        if ("全部".equals(currentCameraFilter) && "全部".equals(currentTimeFilter)) {
            return "显示所有录像";
        } else if ("全部".equals(currentCameraFilter)) {
            return "显示" + currentTimeFilter + "的录像";
        } else if ("全部".equals(currentTimeFilter)) {
            return "显示" + currentCameraFilter + "的录像";
        } else {
            return "显示" + currentCameraFilter + currentTimeFilter + "的录像";
        }
    }

    /**
     * 刷新按钮颜色（用于主题变化时）
     */
    public void refreshButtonColors() {
        // 刷新摄像头按钮颜色
        for (int i = 0; i < cameraButtons.size(); i++) {
            if (cameraFilters[i].equals(currentCameraFilter)) {
                updateCameraButtonStates(i);
                break;
            }
        }

        // 刷新时间按钮颜色
        for (int i = 0; i < timeButtons.size(); i++) {
            if (timeFilters[i].equals(currentTimeFilter)) {
                updateTimeButtonStates(i);
                break;
            }
        }

        LogUtil.d(TAG, "按钮颜色已刷新");
    }
}
