package com.autolink.sbjk.ui;

import android.widget.NumberPicker;

import com.autolink.sbjk.common.util.LogUtil;

import java.util.Calendar;

/**
 * 苹果风格时间选择器管理器
 * 管理月、日、时三个NumberPicker的联动和数据
 */
public class TimePickerManager {
    
    private static final String TAG = "TimePickerManager";
    
    // 时间选择回调接口
    public interface OnTimeSelectedListener {
        void onTimeSelected(int month, int day, int hour);
    }
    
    private OnTimeSelectedListener timeSelectedListener;
    
    // NumberPicker控件
    private NumberPicker monthPicker;
    private NumberPicker dayPicker;
    private NumberPicker hourPicker;
    
    // 当前选中的值
    private int selectedMonth;
    private int selectedDay;
    private int selectedHour;
    
    // 月份天数映射
    private static final int[] DAYS_IN_MONTH = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    /**
     * 设置时间选择监听器
     */
    public void setOnTimeSelectedListener(OnTimeSelectedListener listener) {
        this.timeSelectedListener = listener;
    }
    
    /**
     * 初始化时间选择器
     */
    public void initTimePickers(NumberPicker monthPicker, NumberPicker dayPicker, NumberPicker hourPicker) {
        this.monthPicker = monthPicker;
        this.dayPicker = dayPicker;
        this.hourPicker = hourPicker;
        
        setupMonthPicker();
        setupDayPicker();
        setupHourPicker();
        
        // 设置为当前时间
        setCurrentTime();
        
        LogUtil.d(TAG, "时间选择器初始化完成");
    }
    
    /**
     * 设置月份选择器
     */
    private void setupMonthPicker() {
        monthPicker.setMinValue(1);
        monthPicker.setMaxValue(12);
        monthPicker.setWrapSelectorWheel(true);
        
        // 设置月份显示格式
        String[] monthDisplayValues = new String[12];
        for (int i = 0; i < 12; i++) {
            monthDisplayValues[i] = String.format("%d月", i + 1);
        }
        monthPicker.setDisplayedValues(monthDisplayValues);
        
        // 设置月份变化监听器
        monthPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            selectedMonth = newVal;
            updateDayPicker(); // 月份变化时更新日期范围
            notifyTimeSelected();
        });
    }
    
    /**
     * 设置日期选择器
     */
    private void setupDayPicker() {
        dayPicker.setMinValue(1);
        dayPicker.setWrapSelectorWheel(true);
        
        // 日期变化监听器
        dayPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            selectedDay = newVal;
            notifyTimeSelected();
        });
    }
    
    /**
     * 设置小时选择器
     */
    private void setupHourPicker() {
        hourPicker.setMinValue(0);
        hourPicker.setMaxValue(23);
        hourPicker.setWrapSelectorWheel(true);
        
        // 设置小时显示格式
        String[] hourDisplayValues = new String[24];
        for (int i = 0; i < 24; i++) {
            hourDisplayValues[i] = String.format("%02d时", i);
        }
        hourPicker.setDisplayedValues(hourDisplayValues);
        
        // 小时变化监听器
        hourPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            selectedHour = newVal;
            notifyTimeSelected();
        });
    }
    
    /**
     * 更新日期选择器的范围（根据月份）
     */
    private void updateDayPicker() {
        int daysInMonth = getDaysInMonth(selectedMonth);
        dayPicker.setMaxValue(daysInMonth);
        
        // 设置日期显示格式
        String[] dayDisplayValues = new String[daysInMonth];
        for (int i = 0; i < daysInMonth; i++) {
            dayDisplayValues[i] = String.format("%d日", i + 1);
        }
        dayPicker.setDisplayedValues(dayDisplayValues);
        
        // 如果当前选中的日期超出了新月份的范围，调整到最大值
        if (selectedDay > daysInMonth) {
            selectedDay = daysInMonth;
            dayPicker.setValue(selectedDay);
        }
    }
    
    /**
     * 获取指定月份的天数
     */
    private int getDaysInMonth(int month) {
        if (month < 1 || month > 12) {
            return 31;
        }
        
        int days = DAYS_IN_MONTH[month - 1];
        
        // 处理闰年的2月
        if (month == 2) {
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            if (isLeapYear(currentYear)) {
                days = 29;
            }
        }
        
        return days;
    }
    
    /**
     * 判断是否为闰年
     */
    private boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
    
    /**
     * 设置为当前时间
     */
    private void setCurrentTime() {
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH从0开始
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
        
        setTime(currentMonth, currentDay, currentHour);
    }
    
    /**
     * 设置指定时间
     */
    public void setTime(int month, int day, int hour) {
        selectedMonth = month;
        selectedDay = day;
        selectedHour = hour;
        
        if (monthPicker != null) {
            monthPicker.setValue(month);
        }
        
        updateDayPicker();
        
        if (dayPicker != null) {
            dayPicker.setValue(day);
        }
        
        if (hourPicker != null) {
            hourPicker.setValue(hour);
        }
        
        LogUtil.d(TAG, "设置时间: " + month + "月" + day + "日" + hour + "时");
    }
    
    /**
     * 获取当前选中的时间
     */
    public int getSelectedMonth() {
        return selectedMonth;
    }
    
    public int getSelectedDay() {
        return selectedDay;
    }
    
    public int getSelectedHour() {
        return selectedHour;
    }
    
    /**
     * 获取格式化的时间字符串
     */
    public String getFormattedTime() {
        return String.format("%d月%d日%d时", selectedMonth, selectedDay, selectedHour);
    }
    
    /**
     * 通知时间选择监听器
     */
    private void notifyTimeSelected() {
        if (timeSelectedListener != null) {
            timeSelectedListener.onTimeSelected(selectedMonth, selectedDay, selectedHour);
        }
    }
    
    /**
     * 重置到当前时间
     */
    public void resetToCurrentTime() {
        setCurrentTime();
        notifyTimeSelected();
    }

    /**
     * 重置时间筛选（不触发回调）
     * 用于"全部"按钮点击后重置NumberPicker显示状态
     */
    public void resetTimeFilter() {
        setCurrentTime(); // 重置到当前时间显示
        LogUtil.d(TAG, "时间筛选已重置为全部");
    }
}
