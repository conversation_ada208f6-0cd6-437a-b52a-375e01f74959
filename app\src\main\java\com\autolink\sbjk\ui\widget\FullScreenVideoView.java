package com.autolink.sbjk.ui.widget;

import android.content.Context;
import android.media.MediaPlayer;
import android.util.AttributeSet;
import android.widget.VideoView;
import com.autolink.sbjk.common.util.LogUtil;

/**
 * 自定义VideoView，支持拉伸填满容器
 * 解决默认VideoView保持宽高比导致的黑边问题
 */
public class FullScreenVideoView extends VideoView {
    
    private static final String TAG = "FullScreenVideoView";
    
    public FullScreenVideoView(Context context) {
        super(context);
        init();
    }
    
    public FullScreenVideoView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public FullScreenVideoView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 设置准备监听器，在视频准备完成后设置缩放模式
        setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                // 设置视频缩放模式为拉伸填充（不保持宽高比）
                try {
                    mp.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT);
                    LogUtil.d(TAG, "设置视频缩放模式为拉伸填充");
                } catch (Exception e) {
                    LogUtil.w(TAG, "设置视频缩放模式失败", e);
                }
            }
        });

        // 确保VideoView能正确适应CardView的圆角
        setClipToOutline(true);
    }
    
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // 强制使用父容器的尺寸，不考虑视频的原始宽高比
        int width = getDefaultSize(0, widthMeasureSpec);
        int height = getDefaultSize(0, heightMeasureSpec);
        setMeasuredDimension(width, height);
        
        LogUtil.d(TAG, "VideoView测量尺寸: " + width + "x" + height);
    }
    
    /**
     * 设置视频拉伸填充模式
     */
    public void setStretchToFill() {
        try {
            // 通过反射设置VideoView的缩放模式
            Class<?> videoViewClass = this.getClass().getSuperclass();
            if (videoViewClass != null) {
                java.lang.reflect.Method setVideoScalingMode = videoViewClass.getMethod("setVideoScalingMode", int.class);
                // VIDEO_SCALING_MODE_SCALE_TO_FIT = 1 (拉伸填充，不保持宽高比)
                setVideoScalingMode.invoke(this, 1);
                LogUtil.d(TAG, "通过反射设置VideoView缩放模式为拉伸填充");
            }
        } catch (Exception e) {
            LogUtil.w(TAG, "通过反射设置VideoView缩放模式失败", e);
        }
    }
}
