<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Sbjk" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.Sbjk" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        <!-- 全屏设置 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/colorPrimaryDark</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    
    <!-- 组件样式定义 -->
    <style name="CustomButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:drawableTint">@color/white</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    
    <style name="CustomTextView">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
</resources>